/**
 * Cache Library
 * 
 * Provides caching functionality using Redis or in-memory storage
 * for development environments.
 */

import Redis from 'ioredis';

// Global cache instance
let cacheInstance = null;

/**
 * Initialize cache connection
 */
function initializeCache() {
  if (cacheInstance) {
    return cacheInstance;
  }

  try {
    // Try to connect to Redis if configuration is available
    if (process.env.REDIS_HOST || process.env.REDIS_URL) {
      const redisConfig = process.env.REDIS_URL ? 
        process.env.REDIS_URL : 
        {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT) || 6379,
          password: process.env.REDIS_PASSWORD || undefined,
          db: parseInt(process.env.REDIS_DB) || 0,
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
        };

      cacheInstance = new Redis(redisConfig);

      cacheInstance.on('error', (error) => {
        console.warn('Redis connection error, falling back to memory cache:', error.message);
        cacheInstance = new MemoryCache();
      });

      cacheInstance.on('connect', () => {
        console.log('✅ Redis cache connected successfully');
      });

      // Test connection
      cacheInstance.ping().catch(() => {
        console.warn('Redis ping failed, using memory cache');
        cacheInstance = new MemoryCache();
      });

    } else {
      console.log('No Redis configuration found, using memory cache');
      cacheInstance = new MemoryCache();
    }
  } catch (error) {
    console.warn('Failed to initialize Redis, using memory cache:', error.message);
    cacheInstance = new MemoryCache();
  }

  return cacheInstance;
}

/**
 * In-memory cache implementation for development
 */
class MemoryCache {
  constructor() {
    this.store = new Map();
    this.expirations = new Map();
    
    // Clean up expired keys every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  async get(key) {
    if (this.isExpired(key)) {
      this.del(key);
      return null;
    }
    return this.store.get(key) || null;
  }

  async set(key, value, ttlSeconds = null) {
    this.store.set(key, value);
    if (ttlSeconds) {
      this.expirations.set(key, Date.now() + (ttlSeconds * 1000));
    }
    return 'OK';
  }

  async del(key) {
    this.store.delete(key);
    this.expirations.delete(key);
    return 1;
  }

  async exists(key) {
    if (this.isExpired(key)) {
      this.del(key);
      return 0;
    }
    return this.store.has(key) ? 1 : 0;
  }

  async expire(key, seconds) {
    if (this.store.has(key)) {
      this.expirations.set(key, Date.now() + (seconds * 1000));
      return 1;
    }
    return 0;
  }

  async ttl(key) {
    const expiration = this.expirations.get(key);
    if (!expiration) return -1;
    const remaining = Math.ceil((expiration - Date.now()) / 1000);
    return remaining > 0 ? remaining : -2;
  }

  async ping() {
    return 'PONG';
  }

  async flushall() {
    this.store.clear();
    this.expirations.clear();
    return 'OK';
  }

  // Redis-like sorted set operations for rate limiting
  async zadd(key, score, member) {
    let set = this.store.get(key) || [];
    // Remove existing member
    set = set.filter(item => item.member !== member);
    // Add new member
    set.push({ score, member });
    // Sort by score
    set.sort((a, b) => a.score - b.score);
    this.store.set(key, set);
    return 1;
  }

  async zremrangebyscore(key, min, max) {
    const set = this.store.get(key) || [];
    const filtered = set.filter(item => item.score < min || item.score > max);
    this.store.set(key, filtered);
    return set.length - filtered.length;
  }

  async zcard(key) {
    const set = this.store.get(key) || [];
    return set.length;
  }

  async zrem(key, member) {
    const set = this.store.get(key) || [];
    const filtered = set.filter(item => item.member !== member);
    this.store.set(key, filtered);
    return set.length - filtered.length;
  }

  async zcount(key, min, max) {
    const set = this.store.get(key) || [];
    return set.filter(item => item.score >= min && item.score <= max).length;
  }

  // Pipeline support (simplified)
  pipeline() {
    const commands = [];
    return {
      zadd: (key, score, member) => {
        commands.push(['zadd', key, score, member]);
        return this;
      },
      zremrangebyscore: (key, min, max) => {
        commands.push(['zremrangebyscore', key, min, max]);
        return this;
      },
      zcard: (key) => {
        commands.push(['zcard', key]);
        return this;
      },
      expire: (key, seconds) => {
        commands.push(['expire', key, seconds]);
        return this;
      },
      exec: async () => {
        const results = [];
        for (const [cmd, ...args] of commands) {
          try {
            const result = await this[cmd](...args);
            results.push([null, result]);
          } catch (error) {
            results.push([error, null]);
          }
        }
        return results;
      }
    };
  }

  async disconnect() {
    // No-op for memory cache
    return 'OK';
  }

  isExpired(key) {
    const expiration = this.expirations.get(key);
    return expiration && Date.now() > expiration;
  }

  cleanup() {
    const now = Date.now();
    for (const [key, expiration] of this.expirations.entries()) {
      if (now > expiration) {
        this.del(key);
      }
    }
  }
}

/**
 * Cache key generators
 */
export const CacheKeys = {
  user: (userId) => `user:${userId}`,
  session: (sessionId) => `session:${sessionId}`,
  rateLimit: (identifier, action) => `rate_limit:${action}:${identifier}`,
  apiKey: (userId) => `api_key:${userId}`,
  usage: (userId, period) => `usage:${userId}:${period}`,
  subscription: (userId) => `subscription:${userId}`,
  
  // Temporary cache keys
  temp: (key, ttl = 300) => ({ key: `temp:${key}`, ttl }), // 5 minutes default
  
  // Security-related cache keys
  loginAttempts: (identifier) => `login_attempts:${identifier}`,
  passwordReset: (token) => `password_reset:${token}`,
  emailVerification: (token) => `email_verification:${token}`,
};

/**
 * Cache utility functions
 */
export const CacheUtils = {
  /**
   * Get or set cache value with a fallback function
   */
  async getOrSet(key, fallbackFn, ttlSeconds = 300) {
    try {
      const cached = await cache.get(key);
      if (cached !== null) {
        return typeof cached === 'string' ? JSON.parse(cached) : cached;
      }
      
      const value = await fallbackFn();
      if (value !== null && value !== undefined) {
        await cache.set(key, JSON.stringify(value), ttlSeconds);
      }
      return value;
    } catch (error) {
      console.error('Cache getOrSet error:', error);
      return await fallbackFn();
    }
  },

  /**
   * Invalidate multiple cache keys by pattern
   */
  async invalidatePattern(pattern) {
    try {
      if (cache instanceof MemoryCache) {
        // For memory cache, iterate through keys
        const keys = Array.from(cache.store.keys()).filter(key => 
          key.includes(pattern.replace('*', ''))
        );
        for (const key of keys) {
          await cache.del(key);
        }
      } else {
        // For Redis, use SCAN
        const keys = await cache.keys(pattern);
        if (keys.length > 0) {
          await cache.del(...keys);
        }
      }
    } catch (error) {
      console.error('Cache invalidatePattern error:', error);
    }
  },

  /**
   * Set cache with automatic JSON serialization
   */
  async setJSON(key, value, ttlSeconds = 300) {
    try {
      await cache.set(key, JSON.stringify(value), ttlSeconds);
    } catch (error) {
      console.error('Cache setJSON error:', error);
    }
  },

  /**
   * Get cache with automatic JSON deserialization
   */
  async getJSON(key) {
    try {
      const value = await cache.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache getJSON error:', error);
      return null;
    }
  }
};

// Initialize and export cache instance
export const cache = initializeCache();

// Export default cache instance
export default cache;
