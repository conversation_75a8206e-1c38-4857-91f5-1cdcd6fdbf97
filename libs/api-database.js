/**
 * API Database Integration Library
 * 
 * This library provides secure communication between the frontend and API database
 * while maintaining database separation for security.
 * 
 * SECURITY: This file should ONLY be imported in server-side code (API routes)
 * NEVER import this in client-side components or pages
 */

import { PrismaClient } from '@prisma/client';

// Ensure this is only used server-side
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: API database client cannot be used on the client side!');
}

// Create separate Prisma client for API database
const globalForApiPrisma = globalThis;

export const apiPrisma = globalForApiPrisma.apiPrisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.POSTGRE_DB_API,
    },
  },
});

if (process.env.NODE_ENV !== 'production') globalForApiPrisma.apiPrisma = apiPrisma;

/**
 * API Database Operations
 * These functions provide secure access to the API database
 */

export class ApiDatabaseService {
  
  /**
   * Create or sync user in API database
   * @param {Object} frontendUser - User data from frontend database
   * @returns {Object} API user data
   */
  static async syncUser(frontendUser) {
    try {
      const { id: frontendUserId, email, name } = frontendUser;
      
      // Check if user already exists in API database
      let apiUser = await apiPrisma.user.findUnique({
        where: { email: email }
      });
      
      if (!apiUser) {
        // Create new user in API database
        apiUser = await apiPrisma.user.create({
          data: {
            email: email,
            name: name || null,
            frontendUserId: frontendUserId,
            tier: 'free', // Default tier
            isActive: true,
            createdAt: new Date()
          }
        });
        
        console.log(`✅ Created new API user for frontend user ${frontendUserId}`);
      } else {
        // Update existing user with frontend user ID if not set
        if (!apiUser.frontendUserId) {
          apiUser = await apiPrisma.user.update({
            where: { id: apiUser.id },
            data: { frontendUserId: frontendUserId }
          });
        }
      }
      
      return apiUser;
    } catch (error) {
      console.error('Error syncing user to API database:', error);
      throw new Error('Failed to sync user with API database');
    }
  }
  
  /**
   * Get user's API key information
   * @param {string} frontendUserId - Frontend user ID
   * @returns {Object} API key information
   */
  static async getUserApiKey(frontendUserId) {
    try {
      const apiUser = await apiPrisma.user.findUnique({
        where: { frontendUserId: frontendUserId },
        include: {
          apiKeys: {
            where: { isActive: true },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });
      
      if (!apiUser) {
        throw new Error('User not found in API database');
      }
      
      const activeApiKey = apiUser.apiKeys[0];
      
      return {
        hasApiKey: !!activeApiKey,
        apiKey: activeApiKey?.keyPreview || null, // Only return preview, never full key
        lastUsed: activeApiKey?.lastUsedAt || null,
        usageCount: activeApiKey?.usageCount || 0,
        createdAt: activeApiKey?.createdAt || null,
        tier: apiUser.tier,
        creditsRemaining: apiUser.creditsRemaining || 0
      };
    } catch (error) {
      console.error('Error getting user API key:', error);
      throw new Error('Failed to retrieve API key information');
    }
  }
  
  /**
   * Generate new API key for user
   * @param {string} frontendUserId - Frontend user ID
   * @returns {Object} New API key information
   */
  static async generateApiKey(frontendUserId) {
    try {
      const apiUser = await apiPrisma.user.findUnique({
        where: { frontendUserId: frontendUserId }
      });
      
      if (!apiUser) {
        throw new Error('User not found in API database');
      }
      
      // Deactivate existing API keys
      await apiPrisma.apiKey.updateMany({
        where: { 
          userId: apiUser.id,
          isActive: true 
        },
        data: { isActive: false }
      });
      
      // Generate new API key
      const crypto = await import('crypto');
      const newApiKey = `sk_live_${crypto.randomBytes(32).toString('hex')}`;
      const hashedKey = crypto.createHash('sha256').update(newApiKey).digest('hex');
      const keyPreview = `${newApiKey.substring(0, 12)}...${newApiKey.substring(newApiKey.length - 4)}`;
      
      // Create new API key record
      const apiKeyRecord = await apiPrisma.apiKey.create({
        data: {
          userId: apiUser.id,
          keyHash: hashedKey,
          keyPreview: keyPreview,
          isActive: true,
          usageCount: 0,
          createdAt: new Date()
        }
      });
      
      // Log the generation for audit
      await apiPrisma.auditLog.create({
        data: {
          userId: apiUser.id,
          action: 'API_KEY_GENERATED',
          details: {
            keyId: apiKeyRecord.id,
            frontendUserId: frontendUserId,
            timestamp: new Date().toISOString()
          }
        }
      });
      
      return {
        success: true,
        apiKey: newApiKey, // Return full key only once during generation
        keyPreview: keyPreview,
        lastUsed: null,
        usageCount: 0,
        createdAt: apiKeyRecord.createdAt
      };
    } catch (error) {
      console.error('Error generating API key:', error);
      throw new Error('Failed to generate API key');
    }
  }
  
  /**
   * Get user's API usage statistics
   * @param {string} frontendUserId - Frontend user ID
   * @returns {Object} Usage statistics
   */
  static async getUserUsageStats(frontendUserId) {
    try {
      const apiUser = await apiPrisma.user.findUnique({
        where: { frontendUserId: frontendUserId },
        include: {
          apiKeys: {
            where: { isActive: true }
          }
        }
      });
      
      if (!apiUser) {
        return {
          creditsUsed: 0,
          creditsRemaining: 0,
          totalRequests: 0,
          tier: 'free'
        };
      }
      
      // Get usage statistics for current month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);
      
      const usageStats = await apiPrisma.usageLog.aggregate({
        where: {
          userId: apiUser.id,
          createdAt: { gte: startOfMonth }
        },
        _sum: {
          creditsUsed: true
        },
        _count: {
          id: true
        }
      });
      
      return {
        creditsUsed: usageStats._sum.creditsUsed || 0,
        creditsRemaining: apiUser.creditsRemaining || 0,
        totalRequests: usageStats._count.id || 0,
        tier: apiUser.tier,
        lastUsed: apiUser.lastApiUsage
      };
    } catch (error) {
      console.error('Error getting usage stats:', error);
      throw new Error('Failed to retrieve usage statistics');
    }
  }
  
  /**
   * Update user's subscription tier
   * @param {string} frontendUserId - Frontend user ID
   * @param {string} newTier - New subscription tier
   * @returns {boolean} Success status
   */
  static async updateUserTier(frontendUserId, newTier) {
    try {
      const apiUser = await apiPrisma.user.findUnique({
        where: { frontendUserId: frontendUserId }
      });

      if (!apiUser) {
        throw new Error('User not found in API database');
      }

      await apiPrisma.user.update({
        where: { id: apiUser.id },
        data: {
          tier: newTier,
          updatedAt: new Date()
        }
      });

      // Log the tier change
      await apiPrisma.auditLog.create({
        data: {
          userId: apiUser.id,
          action: 'TIER_UPDATED',
          details: {
            oldTier: apiUser.tier,
            newTier: newTier,
            frontendUserId: frontendUserId,
            timestamp: new Date().toISOString()
          }
        }
      });

      return true;
    } catch (error) {
      console.error('Error updating user tier:', error);
      throw new Error('Failed to update user tier');
    }
  }

  /**
   * Enhanced sync using AdminApiService (when available)
   * @param {Object} frontendUser - User data from frontend database
   * @returns {Promise<Object>} Enhanced sync result
   */
  static async enhancedSync(frontendUser) {
    try {
      // Try to use AdminApiService for better sync
      const { default: AdminApiService } = await import('./admin-api.js');
      return await AdminApiService.syncUser(frontendUser);
    } catch (error) {
      console.error('Enhanced sync failed, using legacy method:', error);
      // Fallback to legacy sync
      const apiUser = await this.syncUser(frontendUser);
      return {
        success: true,
        apiUser: apiUser,
        apiKey: null // Legacy method doesn't provide API key
      };
    }
  }
}

// Helper function to safely disconnect API Prisma
export async function disconnectApiPrisma() {
  await apiPrisma.$disconnect();
}

// Helper function to check API database connection
export async function checkApiDatabaseConnection() {
  try {
    await apiPrisma.$queryRaw`SELECT 1`;
    return { success: true, message: 'API database connection successful' };
  } catch (error) {
    console.error('API database connection failed:', error);
    return { success: false, message: 'API database connection failed', error: error.message };
  }
}

export default ApiDatabaseService;
