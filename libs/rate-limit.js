/**
 * Rate Limiting Library
 * 
 * Provides rate limiting functionality using Redis or in-memory storage
 * for development environments.
 */

import { cache } from './cache';

/**
 * Check if a request is within rate limits
 * @param {string} key - Unique identifier for the rate limit (e.g., user ID + action)
 * @param {number} limit - Maximum number of requests allowed
 * @param {number} windowSeconds - Time window in seconds
 * @returns {Promise<{allowed: boolean, count: number, resetTime: number}>}
 */
export async function checkRateLimit(key, limit, windowSeconds) {
  try {
    const now = Date.now();
    const windowStart = now - (windowSeconds * 1000);
    
    // Try Redis first, fall back to in-memory for development
    if (cache && typeof cache.zremrangebyscore === 'function') {
      // Redis implementation
      return await redisRateLimit(key, limit, windowSeconds, now, windowStart);
    } else {
      // In-memory implementation for development
      return await memoryRateLimit(key, limit, windowSeconds, now, windowStart);
    }
  } catch (error) {
    console.error('Rate limit check failed:', error);
    // Fail open - allow the request if rate limiting fails
    return {
      allowed: true,
      count: 0,
      resetTime: Date.now() + (windowSeconds * 1000)
    };
  }
}

/**
 * Redis-based rate limiting (production)
 */
async function redisRateLimit(key, limit, windowSeconds, now, windowStart) {
  const pipeline = cache.pipeline();
  
  // Remove expired entries
  pipeline.zremrangebyscore(key, 0, windowStart);
  
  // Count current requests in window
  pipeline.zcard(key);
  
  // Add current request
  pipeline.zadd(key, now, now);
  
  // Set expiration
  pipeline.expire(key, windowSeconds);
  
  const results = await pipeline.exec();
  const count = results[1][1]; // Get count from zcard result
  
  const allowed = count < limit;
  const resetTime = now + (windowSeconds * 1000);
  
  if (!allowed) {
    // Remove the request we just added since it's not allowed
    await cache.zrem(key, now);
  }
  
  return {
    allowed,
    count: allowed ? count + 1 : count,
    resetTime
  };
}

/**
 * In-memory rate limiting (development)
 */
const memoryStore = new Map();

async function memoryRateLimit(key, limit, windowSeconds, now, windowStart) {
  // Clean up expired entries periodically
  if (Math.random() < 0.01) { // 1% chance to clean up
    cleanupMemoryStore();
  }
  
  let requests = memoryStore.get(key) || [];
  
  // Remove expired requests
  requests = requests.filter(timestamp => timestamp > windowStart);
  
  const count = requests.length;
  const allowed = count < limit;
  
  if (allowed) {
    requests.push(now);
    memoryStore.set(key, requests);
  }
  
  const resetTime = now + (windowSeconds * 1000);
  
  return {
    allowed,
    count: allowed ? count + 1 : count,
    resetTime
  };
}

/**
 * Clean up expired entries from memory store
 */
function cleanupMemoryStore() {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  
  for (const [key, requests] of memoryStore.entries()) {
    const validRequests = requests.filter(timestamp => now - timestamp < maxAge);
    if (validRequests.length === 0) {
      memoryStore.delete(key);
    } else if (validRequests.length !== requests.length) {
      memoryStore.set(key, validRequests);
    }
  }
}

/**
 * Rate limit configurations for different endpoints
 */
export const RateLimits = {
  // Authentication endpoints
  LOGIN: { limit: 5, window: 900 }, // 5 attempts per 15 minutes
  SIGNUP: { limit: 3, window: 3600 }, // 3 attempts per hour
  PASSWORD_RESET: { limit: 3, window: 3600 }, // 3 attempts per hour
  
  // API key management
  API_KEY_FETCH: { limit: 10, window: 60 }, // 10 requests per minute
  API_KEY_GENERATE: { limit: 3, window: 3600 }, // 3 generations per hour
  
  // General API endpoints
  GENERAL: { limit: 100, window: 60 }, // 100 requests per minute
  STRICT: { limit: 10, window: 60 }, // 10 requests per minute
  
  // Webhook endpoints
  WEBHOOK: { limit: 50, window: 60 }, // 50 requests per minute
};

/**
 * Helper function to create rate limit middleware
 * @param {string} action - Action name for the rate limit
 * @param {Object} config - Rate limit configuration
 * @returns {Function} Middleware function
 */
export function createRateLimitMiddleware(action, config = RateLimits.GENERAL) {
  return async (req, res, next) => {
    try {
      const clientIP = req.headers['x-forwarded-for'] || 
                      req.headers['x-real-ip'] || 
                      req.connection.remoteAddress || 
                      'unknown';
      
      const userId = req.user?.id || 'anonymous';
      const key = `rate_limit:${action}:${userId}:${clientIP}`;
      
      const result = await checkRateLimit(key, config.limit, config.window);
      
      // Add rate limit headers
      res.setHeader('X-RateLimit-Limit', config.limit);
      res.setHeader('X-RateLimit-Remaining', Math.max(0, config.limit - result.count));
      res.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000));
      
      if (!result.allowed) {
        const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
        res.setHeader('Retry-After', retryAfter);
        
        return res.status(429).json({
          error: 'Too many requests',
          retryAfter: retryAfter
        });
      }
      
      next();
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      // Fail open - continue if rate limiting fails
      next();
    }
  };
}

/**
 * Get rate limit status for a key
 * @param {string} key - Rate limit key
 * @param {number} limit - Rate limit
 * @param {number} windowSeconds - Time window
 * @returns {Promise<{count: number, resetTime: number}>}
 */
export async function getRateLimitStatus(key, limit, windowSeconds) {
  try {
    const now = Date.now();
    const windowStart = now - (windowSeconds * 1000);
    
    if (cache && typeof cache.zcount === 'function') {
      // Redis implementation
      const count = await cache.zcount(key, windowStart, now);
      return {
        count,
        resetTime: now + (windowSeconds * 1000)
      };
    } else {
      // In-memory implementation
      const requests = memoryStore.get(key) || [];
      const validRequests = requests.filter(timestamp => timestamp > windowStart);
      return {
        count: validRequests.length,
        resetTime: now + (windowSeconds * 1000)
      };
    }
  } catch (error) {
    console.error('Get rate limit status failed:', error);
    return {
      count: 0,
      resetTime: Date.now() + (windowSeconds * 1000)
    };
  }
}

export default {
  checkRateLimit,
  RateLimits,
  createRateLimitMiddleware,
  getRateLimitStatus
};
