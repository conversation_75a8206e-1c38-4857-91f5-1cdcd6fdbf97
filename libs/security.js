import { prisma } from './prisma';
import { cache, Cache<PERSON><PERSON>s } from './cache';
import crypto from 'crypto';

// Security: Ensure server-side only
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Security library cannot be used on the client side!');
}

// Security event types
export const SECURITY_EVENT_TYPES = {
  FAILED_LOGIN: 'FAILED_LOGIN',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
  API_KEY_MISUSE: 'API_KEY_MISUSE',
  BRUTE_FORCE_ATTEMPT: 'BRUTE_FORCE_ATTEMPT',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  WEBHOOK_VERIFICATION_FAILED: 'WEBHOOK_VERIFICATION_FAILED',
  CSRF_TOKEN_INVALID: 'CSRF_TOKEN_INVALID'
};

// Security event severities
export const SECURITY_SEVERITIES = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

// Audit log actions
export const AUDIT_ACTIONS = {
  USER_LOGIN: 'USER_LOGIN',
  USER_LOGOUT: 'USER_LOGOUT',
  USER_REGISTER: 'USER_REGISTER',
  API_KEY_GENERATED: 'API_KEY_GENERATED',
  API_KEY_REGENERATED: 'API_KEY_REGENERATED',
  API_KEY_REVOKED: 'API_KEY_REVOKED',
  API_KEY_COPIED: 'API_KEY_COPIED',
  API_KEY_VIEWED: 'API_KEY_VIEWED',
  PASSWORD_CHANGED: 'PASSWORD_CHANGED',
  EMAIL_CHANGED: 'EMAIL_CHANGED',
  PROFILE_UPDATED: 'PROFILE_UPDATED',
  ADMIN_ACCESS: 'ADMIN_ACCESS',
  DATA_EXPORT: 'DATA_EXPORT',
  WEBHOOK_PROCESSED: 'WEBHOOK_PROCESSED'
};

// Security: Log audit events (simplified for frontend)
export async function logAuditEvent(userId, action, details = {}, ipAddress = null, userAgent = null) {
  try {
    // For frontend app, we'll just log to console and cache
    // The API app will handle detailed audit logging
    const auditData = {
      userId,
      action,
      details: {
        ...details,
        timestamp: new Date().toISOString()
      },
      ipAddress,
      userAgent,
      success: details.success !== false
    };

    console.log('Audit Event:', auditData);

    // Cache for potential forwarding to API app
    await cache.set(
      `audit:${userId}:${Date.now()}`,
      JSON.stringify(auditData),
      3600 // 1 hour
    ).catch(err => console.error('Failed to cache audit event:', err));

  } catch (error) {
    console.error('Failed to log audit event:', error);
    // Don't throw - audit logging should not break the main flow
  }
}

// Security: Log security events (simplified for frontend)
export async function logSecurityEvent(type, severity, description, metadata = {}, ipAddress = null, userAgent = null, userId = null) {
  try {
    const event = {
      id: crypto.randomUUID(),
      type,
      severity,
      description,
      ipAddress,
      userAgent,
      userId,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      },
      createdAt: new Date().toISOString()
    };

    console.log('Security Event:', event);

    // Cache critical events for immediate alerting
    if (severity === SECURITY_SEVERITIES.CRITICAL) {
      await cache.set(
        `security_alert:${event.id}`,
        JSON.stringify(event),
        3600 // 1 hour
      ).catch(err => console.error('Failed to cache security event:', err));
    }

    // Cache for potential forwarding to API app
    await cache.set(
      `security:${event.id}`,
      JSON.stringify(event),
      3600 // 1 hour
    ).catch(err => console.error('Failed to cache security event:', err));

    return event;
  } catch (error) {
    console.error('Failed to log security event:', error);
    // Don't throw - security logging should not break the main flow
  }
}

// Security: Check for suspicious activity patterns (simplified for frontend)
export async function detectSuspiciousActivity(userId, ipAddress, action) {
  try {
    // For frontend app, use cache-based detection
    const cacheKey = `activity:${userId}:${action}`;
    const recentActivity = await cache.get(cacheKey).catch(() => null);

    let activityCount = 0;
    if (recentActivity) {
      const activities = JSON.parse(recentActivity);
      activityCount = activities.length;

      // Add current activity
      activities.push({
        timestamp: Date.now(),
        ipAddress,
        action
      });

      // Keep only last hour
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      const recentActivities = activities.filter(a => a.timestamp > oneHourAgo);

      await cache.set(cacheKey, JSON.stringify(recentActivities), 3600);
      activityCount = recentActivities.length;
    } else {
      // First activity
      await cache.set(cacheKey, JSON.stringify([{
        timestamp: Date.now(),
        ipAddress,
        action
      }]), 3600);
      activityCount = 1;
    }

    // Detect suspicious patterns
    const suspiciousPatterns = [];

    if (activityCount > 50) {
      suspiciousPatterns.push('High frequency actions');
    }

    if (suspiciousPatterns.length > 0) {
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
        SECURITY_SEVERITIES.HIGH,
        `Suspicious activity detected: ${suspiciousPatterns.join(', ')}`,
        {
          userId,
          action,
          recentActions: activityCount,
          patterns: suspiciousPatterns
        },
        ipAddress,
        null,
        userId
      );

      return { suspicious: true, patterns: suspiciousPatterns };
    }

    return { suspicious: false };
  } catch (error) {
    console.error('Failed to detect suspicious activity:', error);
    return { suspicious: false, error: error.message };
  }
}

// Security: Account lockout management
export async function checkAccountLockout(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        loginAttempts: true,
        lockedUntil: true,
        isActive: true
      }
    });

    if (!user) {
      return { locked: true, reason: 'User not found' };
    }

    if (!user.isActive) {
      return { locked: true, reason: 'Account deactivated' };
    }

    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return { 
        locked: true, 
        reason: 'Account temporarily locked',
        lockedUntil: user.lockedUntil
      };
    }

    return { locked: false, loginAttempts: user.loginAttempts };
  } catch (error) {
    console.error('Failed to check account lockout:', error);
    return { locked: true, reason: 'Security check failed' };
  }
}

// Security: Handle failed login attempt
export async function handleFailedLogin(userId, ipAddress, userAgent) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { loginAttempts: true, email: true }
    });

    if (!user) return;

    const newAttempts = (user.loginAttempts || 0) + 1;
    const maxAttempts = 5;
    const lockoutDuration = 30 * 60 * 1000; // 30 minutes

    let updateData = {
      loginAttempts: newAttempts
    };

    // Lock account after max attempts
    if (newAttempts >= maxAttempts) {
      updateData.lockedUntil = new Date(Date.now() + lockoutDuration);
      
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.ACCOUNT_LOCKED,
        SECURITY_SEVERITIES.HIGH,
        `Account locked after ${maxAttempts} failed login attempts`,
        { userId, attempts: newAttempts },
        ipAddress,
        userAgent,
        userId
      );
    } else {
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.FAILED_LOGIN,
        SECURITY_SEVERITIES.MEDIUM,
        `Failed login attempt ${newAttempts}/${maxAttempts}`,
        { userId, attempts: newAttempts },
        ipAddress,
        userAgent,
        userId
      );
    }

    await prisma.user.update({
      where: { id: userId },
      data: updateData
    });

    // Log audit event
    await logAuditEvent(
      userId,
      AUDIT_ACTIONS.USER_LOGIN,
      { success: false, attempts: newAttempts },
      ipAddress,
      userAgent
    );

  } catch (error) {
    console.error('Failed to handle failed login:', error);
  }
}

// Security: Handle successful login
export async function handleSuccessfulLogin(userId, ipAddress, userAgent) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date()
      }
    });

    await logAuditEvent(
      userId,
      AUDIT_ACTIONS.USER_LOGIN,
      { success: true },
      ipAddress,
      userAgent
    );

    // Check for suspicious login patterns
    await detectSuspiciousActivity(userId, ipAddress, AUDIT_ACTIONS.USER_LOGIN);

  } catch (error) {
    console.error('Failed to handle successful login:', error);
  }
}

// Security: Generate secure tokens
export function generateSecureToken(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

// Security: Hash sensitive data
export function hashSensitiveData(data, salt = null) {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512').toString('hex');
  return { hash, salt: actualSalt };
}

// Security: Verify hashed data
export function verifySensitiveData(data, hash, salt) {
  const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
}

// Security: Get security metrics (simplified for frontend)
export async function getSecurityMetrics(timeframe = '24h') {
  try {
    // For frontend app, return basic metrics from cache
    const metricsKey = `security_metrics:${timeframe}`;
    const cached = await cache.get(metricsKey).catch(() => null);

    if (cached) {
      return JSON.parse(cached);
    }

    // Basic metrics for frontend
    const metrics = {
      timeframe,
      securityEvents: 0,
      auditLogs: 0,
      failedLogins: 0,
      generatedAt: new Date().toISOString(),
      note: 'Detailed metrics available in API app'
    };

    // Cache for 5 minutes
    await cache.set(metricsKey, JSON.stringify(metrics), 300);

    return metrics;

  } catch (error) {
    console.error('Failed to get security metrics:', error);
    return null;
  }
}

export default {
  logAuditEvent,
  logSecurityEvent,
  detectSuspiciousActivity,
  checkAccountLockout,
  handleFailedLogin,
  handleSuccessfulLogin,
  generateSecureToken,
  hashSensitiveData,
  verifySensitiveData,
  getSecurityMetrics,
  SECURITY_EVENT_TYPES,
  SECURITY_SEVERITIES,
  AUDIT_ACTIONS
};
