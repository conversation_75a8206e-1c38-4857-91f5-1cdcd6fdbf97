/**
 * Admin API Service
 * 
 * This service provides communication with the live-api admin endpoints
 * for user management, API key management, and credit management.
 * 
 * SECURITY: This file should ONLY be imported in server-side code (API routes)
 * NEVER import this in client-side components or pages
 */

// Ensure this is only used server-side
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Admin API service cannot be used on the client side!');
}

const LIVE_API_BASE_URL = process.env.LIVE_API_BASE_URL || 'http://localhost:3001';
const ADMIN_API_KEY = process.env.LIVE_API_ADMIN_KEY || 'admin_api_key_super_secure_change_in_production';

/**
 * Admin API Service Class
 * Provides methods to interact with live-api admin endpoints
 */
export class AdminApiService {
  
  /**
   * Make authenticated request to live-api admin endpoint
   * @param {string} endpoint - API endpoint path
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} API response
   */
  static async makeRequest(endpoint, options = {}) {
    try {
      const url = `${LIVE_API_BASE_URL}/admin${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-Admin-API-Key': ADMIN_API_KEY,
          ...options.headers
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Admin API request failed for ${endpoint}:`, error);
      throw new Error(`Admin API request failed: ${error.message}`);
    }
  }

  /**
   * User Management Methods
   */

  /**
   * Register a new user in the live-api database
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Created user data
   */
  static async registerUser(userData) {
    const { email, password, tier_id = 1 } = userData;
    
    return await this.makeRequest('/users/register', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        tier_id
      })
    });
  }

  /**
   * Login user in the live-api database
   * @param {Object} credentials - Login credentials
   * @returns {Promise<Object>} User data and API key
   */
  static async loginUser(credentials) {
    const { email, password } = credentials;
    
    return await this.makeRequest('/users/login', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password
      })
    });
  }

  /**
   * Get user credit information
   * @param {string} userId - User ID in live-api database
   * @returns {Promise<Object>} User credit information
   */
  static async getUserCredits(userId) {
    return await this.makeRequest(`/users/${userId}/credits`);
  }

  /**
   * Add credits to user account
   * @param {string} userId - User ID in live-api database
   * @param {number} creditsToAdd - Number of credits to add
   * @param {string} reason - Reason for credit addition
   * @returns {Promise<Object>} Updated credit information
   */
  static async addUserCredits(userId, creditsToAdd, reason = 'Admin adjustment') {
    return await this.makeRequest(`/users/${userId}/credits`, {
      method: 'POST',
      body: JSON.stringify({
        credits_to_add: creditsToAdd,
        reason
      })
    });
  }

  /**
   * Reset user monthly credits
   * @param {string} userId - User ID in live-api database
   * @returns {Promise<Object>} Reset confirmation
   */
  static async resetUserCredits(userId) {
    return await this.makeRequest(`/users/${userId}/credits/reset`, {
      method: 'POST'
    });
  }

  /**
   * Tier Management Methods
   */

  /**
   * Get all access tiers
   * @returns {Promise<Object>} List of all tiers
   */
  static async getAllTiers() {
    return await this.makeRequest('/tiers');
  }

  /**
   * Enable a tier
   * @param {number} tierId - Tier ID to enable
   * @returns {Promise<Object>} Updated tier information
   */
  static async enableTier(tierId) {
    return await this.makeRequest(`/tiers/${tierId}/enable`, {
      method: 'POST'
    });
  }

  /**
   * Disable a tier
   * @param {number} tierId - Tier ID to disable
   * @returns {Promise<Object>} Updated tier information
   */
  static async disableTier(tierId) {
    return await this.makeRequest(`/tiers/${tierId}/disable`, {
      method: 'POST'
    });
  }

  /**
   * Update tier configuration
   * @param {number} tierId - Tier ID to update
   * @param {Object} tierData - Updated tier data
   * @returns {Promise<Object>} Updated tier information
   */
  static async updateTier(tierId, tierData) {
    return await this.makeRequest(`/tiers/${tierId}`, {
      method: 'PUT',
      body: JSON.stringify(tierData)
    });
  }

  /**
   * Get tier usage statistics
   * @param {number} tierId - Tier ID
   * @returns {Promise<Object>} Tier usage statistics
   */
  static async getTierStats(tierId) {
    return await this.makeRequest(`/tiers/${tierId}/stats`);
  }

  /**
   * Analytics Methods
   */

  /**
   * Get credit usage analytics
   * @param {string} period - Analytics period ('month', 'week', 'day')
   * @param {number} limit - Number of top results to return
   * @returns {Promise<Object>} Credit analytics data
   */
  static async getCreditAnalytics(period = 'month', limit = 10) {
    return await this.makeRequest(`/analytics/credits?period=${period}&limit=${limit}`);
  }

  /**
   * Admin Management Methods
   */

  /**
   * Get current admin info
   * @returns {Promise<Object>} Admin information
   */
  static async getAdminInfo() {
    return await this.makeRequest('/me');
  }

  /**
   * Get all admin users (system admin only)
   * @returns {Promise<Object>} List of admin users
   */
  static async getAllAdmins() {
    return await this.makeRequest('/admins');
  }

  /**
   * Create new admin user (system admin only)
   * @param {Object} adminData - Admin user data
   * @returns {Promise<Object>} Created admin user
   */
  static async createAdmin(adminData) {
    return await this.makeRequest('/admins', {
      method: 'POST',
      body: JSON.stringify(adminData)
    });
  }

  /**
   * Update admin user (system admin only)
   * @param {string} adminId - Admin ID to update
   * @param {Object} adminData - Updated admin data
   * @returns {Promise<Object>} Updated admin user
   */
  static async updateAdmin(adminId, adminData) {
    return await this.makeRequest(`/admins/${adminId}`, {
      method: 'PUT',
      body: JSON.stringify(adminData)
    });
  }

  /**
   * Utility Methods
   */

  /**
   * Test connection to live-api admin endpoints
   * @returns {Promise<boolean>} Connection status
   */
  static async testConnection() {
    try {
      await this.getAdminInfo();
      return true;
    } catch (error) {
      console.error('Live-API connection test failed:', error);
      return false;
    }
  }

  /**
   * Sync user between frontend and live-api databases
   * @param {Object} frontendUser - User data from frontend database
   * @returns {Promise<Object>} Sync result
   */
  static async syncUser(frontendUser) {
    try {
      // Try to login first (user might already exist)
      const tempPassword = `temp_${Date.now()}_${Math.random().toString(36).substring(7)}`;
      
      let apiUser;
      try {
        // Try to register user (will fail if already exists)
        apiUser = await this.registerUser({
          email: frontendUser.email,
          password: tempPassword,
          tier_id: this.mapStripeToTier(frontendUser.priceId)
        });
        
        console.log(`✅ Created new API user for frontend user ${frontendUser.id}`);
      } catch (error) {
        if (error.message.includes('already exists')) {
          // User already exists, that's fine
          console.log(`ℹ️ API user already exists for ${frontendUser.email}`);
          apiUser = { user: { email: frontendUser.email } };
        } else {
          throw error;
        }
      }

      return {
        success: true,
        apiUser: apiUser.user,
        apiKey: apiUser.apiKey
      };
    } catch (error) {
      console.error('User sync failed:', error);
      throw new Error(`Failed to sync user: ${error.message}`);
    }
  }

  /**
   * Map Stripe price ID to live-api tier ID
   * @param {string} priceId - Stripe price ID
   * @returns {number} Live-api tier ID
   */
  static mapStripeToTier(priceId) {
    const tierMapping = {
      'price_starter': 2,    // Basic tier
      'price_pro': 3,        // Premium tier
      'price_enterprise': 4  // Enterprise tier
    };
    
    return tierMapping[priceId] || 1; // Default to free tier
  }
}

export default AdminApiService;
