import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import AdminApiService from "@/libs/admin-api";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

/**
 * User Credit Management API Routes
 * 
 * These routes provide admin functionality for managing user credits
 * in the live-api database
 */

// Helper function to check admin permissions
async function checkAdminPermissions(session) {
  if (!session?.user?.id) {
    throw createAuthError('required');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true, isActive: true }
  });

  if (!user || !user.isActive) {
    throw createAuthError('user_not_found');
  }

  if (!['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw createAuthError('insufficient_permissions');
  }

  return user;
}

// GET - Get user credit information from live-api
export async function GET(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'get-credits');
    const rateLimit = await checkRateLimit(rateLimitKey, 30, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { userId } = params;

    // Get user from frontend database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        hasAccess: true,
        apiUserId: true,
        priceId: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (!user.hasAccess || !user.apiUserId) {
      return NextResponse.json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          hasAccess: false
        },
        credits: {
          credits_remaining: 0,
          credits_used_this_month: 0,
          total_credits_purchased: 0,
          tier_name: 'No Access',
          max_credits_per_month: 0,
          usage_percentage: 0
        },
        recent_usage: [],
        message: 'User does not have API access'
      });
    }

    // Get credit information from live-api
    const creditInfo = await AdminApiService.getUserCredits(user.apiUserId);

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_VIEW_USER_CREDITS',
        details: {
          viewedUserId: userId,
          viewedUserEmail: user.email,
          apiUserId: user.apiUserId,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      ...creditInfo
    });

  } catch (error) {
    console.error('Admin get user credits error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to get user credit information' },
      { status: 500 }
    );
  }
}

// POST - Add credits to user account
export async function POST(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting - 10 credit additions per hour
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'add-credits');
    const rateLimit = await checkRateLimit(rateLimitKey, 10, 3600);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_credit_addition', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { userId } = params;
    const body = await req.json();
    const { credits_to_add, reason = 'Admin adjustment' } = body;

    // Validation
    if (!credits_to_add || credits_to_add <= 0) {
      return NextResponse.json(
        { error: 'credits_to_add must be a positive number' },
        { status: 400 }
      );
    }

    if (credits_to_add > 1000000) {
      return NextResponse.json(
        { error: 'Cannot add more than 1,000,000 credits at once' },
        { status: 400 }
      );
    }

    // Get user from frontend database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        hasAccess: true,
        apiUserId: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (!user.hasAccess || !user.apiUserId) {
      return NextResponse.json(
        { error: 'User does not have API access' },
        { status: 400 }
      );
    }

    // Add credits via live-api
    const result = await AdminApiService.addUserCredits(
      user.apiUserId, 
      credits_to_add, 
      reason
    );

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_ADD_USER_CREDITS',
        details: {
          targetUserId: userId,
          targetUserEmail: user.email,
          apiUserId: user.apiUserId,
          creditsAdded: credits_to_add,
          reason,
          result,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: `Added ${credits_to_add} credits to user account`,
      ...result
    });

  } catch (error) {
    console.error('Admin add user credits error:', error);
    
    // Log failed attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_ADD_USER_CREDITS_FAILED',
          details: {
            targetUserId: params.userId,
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed credit addition:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add credits to user account' },
      { status: 500 }
    );
  }
}

// PUT - Reset user monthly credits
export async function PUT(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting - 5 credit resets per hour
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'reset-credits');
    const rateLimit = await checkRateLimit(rateLimitKey, 5, 3600);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_credit_reset', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { userId } = params;

    // Get user from frontend database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        hasAccess: true,
        apiUserId: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (!user.hasAccess || !user.apiUserId) {
      return NextResponse.json(
        { error: 'User does not have API access' },
        { status: 400 }
      );
    }

    // Reset credits via live-api
    const result = await AdminApiService.resetUserCredits(user.apiUserId);

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_RESET_USER_CREDITS',
        details: {
          targetUserId: userId,
          targetUserEmail: user.email,
          apiUserId: user.apiUserId,
          result,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: 'User monthly credits reset successfully',
      ...result
    });

  } catch (error) {
    console.error('Admin reset user credits error:', error);
    
    // Log failed attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_RESET_USER_CREDITS_FAILED',
          details: {
            targetUserId: params.userId,
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed credit reset:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to reset user monthly credits' },
      { status: 500 }
    );
  }
}
