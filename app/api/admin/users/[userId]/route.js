import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import AdminApiService from "@/libs/admin-api";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

/**
 * Individual User Management API Routes
 * 
 * These routes provide admin functionality for managing individual users
 * across both frontend and live-api databases
 */

// Helper function to check admin permissions
async function checkAdminPermissions(session) {
  if (!session?.user?.id) {
    throw createAuthError('required');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true, isActive: true }
  });

  if (!user || !user.isActive) {
    throw createAuthError('user_not_found');
  }

  if (!['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw createAuthError('insufficient_permissions');
  }

  return user;
}

// GET - Get specific user details with API status
export async function GET(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'get-user');
    const rateLimit = await checkRateLimit(rateLimitKey, 30, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { userId } = params;

    // Get user from frontend database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        hasAccess: true,
        apiUserId: true,
        apiUserEmail: true,
        customerId: true,
        priceId: true,
        lastLoginAt: true,
        loginAttempts: true,
        lockedUntil: true,
        twoFactorEnabled: true,
        createdAt: true,
        updatedAt: true,
        accounts: {
          select: {
            provider: true,
            providerAccountId: true,
            createdAt: true
          }
        },
        auditLogs: {
          select: {
            action: true,
            success: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get API database information if user is synced
    let apiInfo = null;
    if (user.apiUserId && user.hasAccess) {
      try {
        apiInfo = await AdminApiService.getUserCredits(user.apiUserId);
      } catch (error) {
        console.error('Failed to get API info for user:', error);
        apiInfo = { error: 'Failed to fetch API information' };
      }
    }

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_VIEW_USER',
        details: {
          viewedUserId: userId,
          viewedUserEmail: user.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      user,
      apiInfo
    });

  } catch (error) {
    console.error('Admin get user error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to get user information' },
      { status: 500 }
    );
  }
}

// PUT - Update user information
export async function PUT(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'update-user');
    const rateLimit = await checkRateLimit(rateLimitKey, 10, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_user_update', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { userId } = params;
    const body = await req.json();
    const { 
      name, 
      role, 
      isActive, 
      hasAccess, 
      priceId,
      resetLoginAttempts = false 
    } = body;

    // Get current user
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        role: true,
        hasAccess: true,
        apiUserId: true,
        priceId: true
      }
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Build update data
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (role !== undefined) updateData.role = role;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (hasAccess !== undefined) updateData.hasAccess = hasAccess;
    if (priceId !== undefined) updateData.priceId = priceId;
    if (resetLoginAttempts) {
      updateData.loginAttempts = 0;
      updateData.lockedUntil = null;
    }

    // Update user in frontend database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData
    });

    // Handle API sync if access status changed
    let apiSyncResult = null;
    if (hasAccess !== undefined && hasAccess !== currentUser.hasAccess) {
      if (hasAccess && !currentUser.apiUserId) {
        // User gained access, sync with API database
        try {
          apiSyncResult = await AdminApiService.syncUser({
            id: updatedUser.id,
            email: updatedUser.email,
            name: updatedUser.name,
            priceId: updatedUser.priceId
          });

          if (apiSyncResult.success && apiSyncResult.apiUser) {
            await prisma.user.update({
              where: { id: userId },
              data: {
                apiUserId: apiSyncResult.apiUser.id || null,
                apiUserEmail: apiSyncResult.apiUser.email || null
              }
            });
          }
        } catch (error) {
          console.error('API sync failed during user update:', error);
          apiSyncResult = { success: false, error: error.message };
        }
      }
    }

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_UPDATE_USER',
        details: {
          updatedUserId: userId,
          updatedUserEmail: currentUser.email,
          changes: updateData,
          apiSyncResult,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: 'User updated successfully',
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        isActive: updatedUser.isActive,
        hasAccess: updatedUser.hasAccess,
        priceId: updatedUser.priceId,
        updatedAt: updatedUser.updatedAt
      },
      apiSync: apiSyncResult
    });

  } catch (error) {
    console.error('Admin update user error:', error);
    
    // Log failed update attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_UPDATE_USER_FAILED',
          details: {
            targetUserId: params.userId,
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed update:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE - Deactivate user (soft delete)
export async function DELETE(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Only SUPER_ADMIN can delete users
    if (adminUser.role !== 'SUPER_ADMIN') {
      throw createAuthError('insufficient_permissions');
    }

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'delete-user');
    const rateLimit = await checkRateLimit(rateLimitKey, 3, 3600); // 3 per hour

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_user_deletion', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { userId } = params;

    // Get user to delete
    const userToDelete = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, email: true, role: true }
    });

    if (!userToDelete) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent deletion of other admins
    if (['ADMIN', 'SUPER_ADMIN'].includes(userToDelete.role)) {
      return NextResponse.json(
        { error: 'Cannot delete admin users' },
        { status: 403 }
      );
    }

    // Soft delete user (deactivate)
    const deactivatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        hasAccess: false,
        updatedAt: new Date()
      }
    });

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_DELETE_USER',
        details: {
          deletedUserId: userId,
          deletedUserEmail: userToDelete.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: 'User deactivated successfully',
      user: {
        id: deactivatedUser.id,
        email: deactivatedUser.email,
        isActive: deactivatedUser.isActive,
        hasAccess: deactivatedUser.hasAccess
      }
    });

  } catch (error) {
    console.error('Admin delete user error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
