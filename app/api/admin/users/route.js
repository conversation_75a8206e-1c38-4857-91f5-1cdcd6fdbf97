import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import AdminApiService from "@/libs/admin-api";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

/**
 * Admin User Management API Routes
 * 
 * These routes provide admin functionality for managing users
 * across both frontend and live-api databases
 */

// Helper function to check admin permissions
async function checkAdminPermissions(session) {
  if (!session?.user?.id) {
    throw createAuthError('required');
  }

  // Check if user has admin role
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true, isActive: true }
  });

  if (!user || !user.isActive) {
    throw createAuthError('user_not_found');
  }

  if (!['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw createAuthError('insufficient_permissions');
  }

  return user;
}

// GET - List all users with their API status
export async function GET(req) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting - 20 requests per minute for admin
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'list-users');
    const rateLimit = await checkRateLimit(rateLimitKey, 20, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    // Get URL parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page')) || 1;
    const limit = Math.min(parseInt(url.searchParams.get('limit')) || 20, 100);
    const search = url.searchParams.get('search') || '';
    const role = url.searchParams.get('role') || '';

    // Build where clause for filtering
    const where = {
      AND: [
        search ? {
          OR: [
            { email: { contains: search, mode: 'insensitive' } },
            { name: { contains: search, mode: 'insensitive' } }
          ]
        } : {},
        role ? { role: role } : {}
      ].filter(condition => Object.keys(condition).length > 0)
    };

    // Get users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          hasAccess: true,
          apiUserId: true,
          apiUserEmail: true,
          customerId: true,
          priceId: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.user.count({ where })
    ]);

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_LIST_USERS',
        details: {
          page,
          limit,
          search,
          role,
          resultCount: users.length,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      users,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      },
      filters: { search, role }
    });

  } catch (error) {
    console.error('Admin list users error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to list users' },
      { status: 500 }
    );
  }
}

// POST - Create new user (both frontend and API databases)
export async function POST(req) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting - 5 user creations per hour for admin
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'create-user');
    const rateLimit = await checkRateLimit(rateLimitKey, 5, 3600);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_user_creation', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    // Parse request body
    const body = await req.json();
    const { 
      name, 
      email, 
      role = 'USER', 
      hasAccess = false, 
      priceId = null,
      tier_id = 1 
    } = body;

    // Validation
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check if user already exists in frontend database
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email' },
        { status: 409 }
      );
    }

    // Create user in frontend database
    const frontendUser = await prisma.user.create({
      data: {
        name,
        email: email.toLowerCase(),
        role,
        hasAccess,
        priceId,
        isActive: true
      }
    });

    let apiSyncResult = null;
    let apiError = null;

    // Try to sync with API database if user has access
    if (hasAccess) {
      try {
        apiSyncResult = await AdminApiService.syncUser({
          id: frontendUser.id,
          email: frontendUser.email,
          name: frontendUser.name,
          priceId: frontendUser.priceId
        });

        // Update frontend user with API user information
        if (apiSyncResult.success && apiSyncResult.apiUser) {
          await prisma.user.update({
            where: { id: frontendUser.id },
            data: {
              apiUserId: apiSyncResult.apiUser.id || null,
              apiUserEmail: apiSyncResult.apiUser.email || null
            }
          });
        }
      } catch (error) {
        console.error('API sync failed during user creation:', error);
        apiError = error.message;
      }
    }

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_CREATE_USER',
        details: {
          createdUserId: frontendUser.id,
          createdUserEmail: frontendUser.email,
          role,
          hasAccess,
          apiSyncSuccess: !!apiSyncResult?.success,
          apiError,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: {
        id: frontendUser.id,
        name: frontendUser.name,
        email: frontendUser.email,
        role: frontendUser.role,
        hasAccess: frontendUser.hasAccess,
        isActive: frontendUser.isActive,
        createdAt: frontendUser.createdAt
      },
      apiSync: {
        success: !!apiSyncResult?.success,
        apiKey: apiSyncResult?.apiKey || null,
        error: apiError
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Admin create user error:', error);
    
    // Log failed creation attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_CREATE_USER_FAILED',
          details: {
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed creation:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
