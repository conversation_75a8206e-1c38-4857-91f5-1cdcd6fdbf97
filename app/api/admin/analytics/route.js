import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import AdminApiService from "@/libs/admin-api";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

/**
 * Admin Analytics API Routes
 * 
 * These routes provide analytics data for the admin dashboard
 * combining data from both frontend and live-api databases
 */

// Helper function to check admin permissions
async function checkAdminPermissions(session) {
  if (!session?.user?.id) {
    throw createAuthError('required');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true, isActive: true }
  });

  if (!user || !user.isActive) {
    throw createAuthError('user_not_found');
  }

  if (!['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw createAuthError('insufficient_permissions');
  }

  return user;
}

// GET - Get comprehensive analytics data
export async function GET(req) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'analytics');
    const rateLimit = await checkRateLimit(rateLimitKey, 10, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_analytics', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    // Get URL parameters
    const url = new URL(req.url);
    const period = url.searchParams.get('period') || 'month';
    const limit = Math.min(parseInt(url.searchParams.get('limit')) || 10, 50);

    // Get frontend database analytics
    const frontendAnalytics = await getFrontendAnalytics(period);

    // Get live-api analytics
    let apiAnalytics = null;
    try {
      apiAnalytics = await AdminApiService.getCreditAnalytics(period, limit);
    } catch (error) {
      console.error('Failed to get API analytics:', error);
      apiAnalytics = { error: 'Failed to fetch API analytics' };
    }

    // Combine analytics data
    const combinedAnalytics = {
      period,
      frontend: frontendAnalytics,
      api: apiAnalytics,
      summary: {
        total_users: frontendAnalytics.total_users,
        active_users: frontendAnalytics.active_users,
        users_with_access: frontendAnalytics.users_with_access,
        api_users_synced: frontendAnalytics.api_users_synced,
        total_api_requests: apiAnalytics?.analytics?.overall?.total_requests || 0,
        total_credits_consumed: apiAnalytics?.analytics?.overall?.total_credits_consumed || 0
      }
    };

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_VIEW_ANALYTICS',
        details: {
          period,
          limit,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      analytics: combinedAnalytics
    });

  } catch (error) {
    console.error('Admin analytics error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to get analytics data' },
      { status: 500 }
    );
  }
}

/**
 * Get analytics data from frontend database
 * @param {string} period - Time period for analytics
 * @returns {Object} Frontend analytics data
 */
async function getFrontendAnalytics(period) {
  try {
    // Calculate date range based on period
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'day':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Get user statistics
    const [
      totalUsers,
      activeUsers,
      usersWithAccess,
      apiUsersSynced,
      newUsersInPeriod,
      usersByRole,
      usersByProvider,
      recentActivity
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      
      // Active users
      prisma.user.count({
        where: { isActive: true }
      }),
      
      // Users with API access
      prisma.user.count({
        where: { hasAccess: true }
      }),
      
      // Users synced with API database
      prisma.user.count({
        where: { 
          apiUserId: { not: null },
          hasAccess: true
        }
      }),
      
      // New users in period
      prisma.user.count({
        where: {
          createdAt: { gte: startDate }
        }
      }),
      
      // Users by role
      prisma.user.groupBy({
        by: ['role'],
        _count: { id: true },
        where: { isActive: true }
      }),
      
      // Users by auth provider
      prisma.account.groupBy({
        by: ['provider'],
        _count: { id: true }
      }),
      
      // Recent user activity (audit logs)
      prisma.auditLog.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        select: {
          action: true,
          success: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: 100
      })
    ]);

    // Process activity data
    const activityByAction = {};
    const activityByDay = {};
    
    recentActivity.forEach(log => {
      // Count by action
      if (!activityByAction[log.action]) {
        activityByAction[log.action] = { total: 0, successful: 0, failed: 0 };
      }
      activityByAction[log.action].total++;
      if (log.success) {
        activityByAction[log.action].successful++;
      } else {
        activityByAction[log.action].failed++;
      }
      
      // Count by day
      const day = log.createdAt.toISOString().split('T')[0];
      if (!activityByDay[day]) {
        activityByDay[day] = 0;
      }
      activityByDay[day]++;
    });

    return {
      total_users: totalUsers,
      active_users: activeUsers,
      users_with_access: usersWithAccess,
      api_users_synced: apiUsersSynced,
      new_users_in_period: newUsersInPeriod,
      users_by_role: usersByRole.reduce((acc, item) => {
        acc[item.role] = item._count.id;
        return acc;
      }, {}),
      users_by_provider: usersByProvider.reduce((acc, item) => {
        acc[item.provider] = item._count.id;
        return acc;
      }, {}),
      activity_summary: {
        total_actions: recentActivity.length,
        successful_actions: recentActivity.filter(log => log.success).length,
        failed_actions: recentActivity.filter(log => !log.success).length,
        by_action: activityByAction,
        by_day: activityByDay
      }
    };
    
  } catch (error) {
    console.error('Error getting frontend analytics:', error);
    return {
      error: 'Failed to get frontend analytics',
      total_users: 0,
      active_users: 0,
      users_with_access: 0,
      api_users_synced: 0
    };
  }
}
