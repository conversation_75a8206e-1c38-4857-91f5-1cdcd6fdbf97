import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import AdminApiService from "@/libs/admin-api";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

/**
 * Tier Management API Routes
 * 
 * These routes provide admin functionality for managing access tiers
 * in the live-api database
 */

// Helper function to check admin permissions
async function checkAdminPermissions(session) {
  if (!session?.user?.id) {
    throw createAuthError('required');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true, isActive: true }
  });

  if (!user || !user.isActive) {
    throw createAuthError('user_not_found');
  }

  if (!['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw createAuthError('insufficient_permissions');
  }

  return user;
}

// GET - Get all access tiers from live-api
export async function GET(req) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'get-tiers');
    const rateLimit = await checkRateLimit(rateLimitKey, 20, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    // Get tiers from live-api
    const tiersResult = await AdminApiService.getAllTiers();

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_VIEW_TIERS',
        details: {
          tierCount: tiersResult.tiers?.length || 0,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      ...tiersResult
    });

  } catch (error) {
    console.error('Admin get tiers error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to get access tiers' },
      { status: 500 }
    );
  }
}

// POST - Update tier configuration (enable/disable/modify)
export async function POST(req) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Only SUPER_ADMIN can modify tiers
    if (adminUser.role !== 'SUPER_ADMIN') {
      throw createAuthError('insufficient_permissions');
    }

    // Rate limiting - 5 tier modifications per hour
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'modify-tiers');
    const rateLimit = await checkRateLimit(rateLimitKey, 5, 3600);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_tier_modification', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const body = await req.json();
    const { action, tierId, tierData } = body;

    if (!action || !tierId) {
      return NextResponse.json(
        { error: 'Action and tierId are required' },
        { status: 400 }
      );
    }

    let result;
    let actionDescription;

    switch (action) {
      case 'enable':
        result = await AdminApiService.enableTier(tierId);
        actionDescription = 'enabled';
        break;
        
      case 'disable':
        result = await AdminApiService.disableTier(tierId);
        actionDescription = 'disabled';
        break;
        
      case 'update':
        if (!tierData) {
          return NextResponse.json(
            { error: 'tierData is required for update action' },
            { status: 400 }
          );
        }
        result = await AdminApiService.updateTier(tierId, tierData);
        actionDescription = 'updated';
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be enable, disable, or update' },
          { status: 400 }
        );
    }

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_MODIFY_TIER',
        details: {
          action,
          tierId,
          tierData: tierData || null,
          result,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: `Tier ${actionDescription} successfully`,
      ...result
    });

  } catch (error) {
    console.error('Admin modify tier error:', error);
    
    // Log failed attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_MODIFY_TIER_FAILED',
          details: {
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed tier modification:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to modify tier' },
      { status: 500 }
    );
  }
}
