import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import AdminApiService from "@/libs/admin-api";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

/**
 * Individual Tier Management API Routes
 * 
 * These routes provide admin functionality for managing specific access tiers
 * in the live-api database
 */

// Helper function to check admin permissions
async function checkAdminPermissions(session, requireSuperAdmin = false) {
  if (!session?.user?.id) {
    throw createAuthError('required');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true, isActive: true }
  });

  if (!user || !user.isActive) {
    throw createAuthError('user_not_found');
  }

  if (requireSuperAdmin && user.role !== 'SUPER_ADMIN') {
    throw createAuthError('insufficient_permissions');
  } else if (!['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw createAuthError('insufficient_permissions');
  }

  return user;
}

// GET - Get specific tier statistics
export async function GET(req, { params }) {
  try {
    // Authentication and authorization check
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session);

    // Rate limiting
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'get-tier-stats');
    const rateLimit = await checkRateLimit(rateLimitKey, 30, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { tierId } = params;

    // Validate tierId
    const tierIdNum = parseInt(tierId);
    if (isNaN(tierIdNum)) {
      return NextResponse.json(
        { error: 'Invalid tier ID' },
        { status: 400 }
      );
    }

    // Get tier statistics from live-api
    const tierStats = await AdminApiService.getTierStats(tierIdNum);

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_VIEW_TIER_STATS',
        details: {
          tierId: tierIdNum,
          tierName: tierStats.tier?.name,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      ...tierStats
    });

  } catch (error) {
    console.error('Admin get tier stats error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to get tier statistics' },
      { status: 500 }
    );
  }
}

// PUT - Update tier configuration
export async function PUT(req, { params }) {
  try {
    // Authentication and authorization check (requires SUPER_ADMIN)
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session, true);

    // Rate limiting - 3 tier updates per hour
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'update-tier');
    const rateLimit = await checkRateLimit(rateLimitKey, 3, 3600);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_tier_update', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { tierId } = params;
    const body = await req.json();

    // Validate tierId
    const tierIdNum = parseInt(tierId);
    if (isNaN(tierIdNum)) {
      return NextResponse.json(
        { error: 'Invalid tier ID' },
        { status: 400 }
      );
    }

    // Validate update data
    const allowedFields = [
      'name',
      'description',
      'max_credits_per_month',
      'max_requests_per_minute',
      'max_websocket_connections',
      'allowed_endpoints',
      'allowed_streams',
      'price_per_month'
    ];

    const updateData = {};
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Validate specific fields
    if (updateData.max_credits_per_month !== undefined && updateData.max_credits_per_month < -1) {
      return NextResponse.json(
        { error: 'max_credits_per_month must be -1 (unlimited) or positive number' },
        { status: 400 }
      );
    }

    if (updateData.max_requests_per_minute !== undefined && updateData.max_requests_per_minute <= 0) {
      return NextResponse.json(
        { error: 'max_requests_per_minute must be a positive number' },
        { status: 400 }
      );
    }

    if (updateData.price_per_month !== undefined && updateData.price_per_month < 0) {
      return NextResponse.json(
        { error: 'price_per_month must be a non-negative number' },
        { status: 400 }
      );
    }

    // Update tier via live-api
    const result = await AdminApiService.updateTier(tierIdNum, updateData);

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_UPDATE_TIER',
        details: {
          tierId: tierIdNum,
          updateData,
          result,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: 'Tier updated successfully',
      ...result
    });

  } catch (error) {
    console.error('Admin update tier error:', error);
    
    // Log failed attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_UPDATE_TIER_FAILED',
          details: {
            tierId: params.tierId,
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed tier update:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update tier' },
      { status: 500 }
    );
  }
}

// POST - Enable/Disable tier
export async function POST(req, { params }) {
  try {
    // Authentication and authorization check (requires SUPER_ADMIN)
    const session = await getServerSession(authOptions);
    const adminUser = await checkAdminPermissions(session, true);

    // Rate limiting - 5 tier enable/disable per hour
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`admin:${session.user.id}:${clientIP}`, 'toggle-tier');
    const rateLimit = await checkRateLimit(rateLimitKey, 5, 3600);

    if (!rateLimit.allowed) {
      throw createRateLimitError('admin_tier_toggle', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const { tierId } = params;
    const body = await req.json();
    const { action } = body;

    // Validate tierId
    const tierIdNum = parseInt(tierId);
    if (isNaN(tierIdNum)) {
      return NextResponse.json(
        { error: 'Invalid tier ID' },
        { status: 400 }
      );
    }

    // Validate action
    if (!['enable', 'disable'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "enable" or "disable"' },
        { status: 400 }
      );
    }

    let result;
    if (action === 'enable') {
      result = await AdminApiService.enableTier(tierIdNum);
    } else {
      result = await AdminApiService.disableTier(tierIdNum);
    }

    // Log admin action
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: `ADMIN_${action.toUpperCase()}_TIER`,
        details: {
          tierId: tierIdNum,
          action,
          result,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log admin action:', err));

    return NextResponse.json({
      success: true,
      message: `Tier ${action}d successfully`,
      ...result
    });

  } catch (error) {
    console.error('Admin toggle tier error:', error);
    
    // Log failed attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'ADMIN_TOGGLE_TIER_FAILED',
          details: {
            tierId: params.tierId,
            error: error.message,
            timestamp: new Date().toISOString()
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed tier toggle:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: `Failed to ${body.action} tier` },
      { status: 500 }
    );
  }
}
