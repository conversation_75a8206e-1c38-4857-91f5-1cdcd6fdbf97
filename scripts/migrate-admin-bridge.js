#!/usr/bin/env node

/**
 * Database Migration Script for Admin Bridge System
 * 
 * This script migrates the database to add the new admin bridge functionality
 * including audit logs and admin users tables.
 */

import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting Admin Bridge Migration...');

  try {
    // Step 1: Generate Prisma client with new schema
    console.log('📝 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });

    // Step 2: Push schema changes to database
    console.log('🗄️  Pushing schema changes to database...');
    execSync('npx prisma db push', { stdio: 'inherit' });

    // Step 3: Create default admin user if none exists
    console.log('👤 Creating default admin user...');
    await createDefaultAdmin();

    // Step 4: Test database connections
    console.log('🔗 Testing database connections...');
    await testConnections();

    console.log('✅ Admin Bridge Migration completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Update your environment variables with LIVE_API_BASE_URL and LIVE_API_ADMIN_KEY');
    console.log('2. Test the admin endpoints');
    console.log('3. Create admin dashboard components');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function createDefaultAdmin() {
  try {
    // Check if any admin users exist
    const existingAdmins = await prisma.adminUser.count();
    
    if (existingAdmins === 0) {
      // Create default admin user
      const defaultAdmin = await prisma.adminUser.create({
        data: {
          name: 'System Administrator',
          email: '<EMAIL>',
          role: 'SUPER_ADMIN',
          permissions: [
            'tiers:read',
            'tiers:write',
            'users:read',
            'users:write',
            'analytics:read',
            'system:admin'
          ],
          isActive: true,
          apiKey: generateApiKey()
        }
      });

      console.log(`✅ Created default admin user: ${defaultAdmin.email}`);
      console.log(`🔑 Admin API Key: ${defaultAdmin.apiKey}`);
      console.log('⚠️  Please save this API key securely - it will not be shown again!');
    } else {
      console.log('ℹ️  Admin users already exist, skipping default admin creation');
    }

    // Check if any regular users have admin roles
    const adminUsers = await prisma.user.findMany({
      where: {
        role: {
          in: ['ADMIN', 'SUPER_ADMIN']
        }
      },
      select: {
        id: true,
        email: true,
        role: true
      }
    });

    if (adminUsers.length > 0) {
      console.log(`ℹ️  Found ${adminUsers.length} users with admin roles:`);
      adminUsers.forEach(user => {
        console.log(`   - ${user.email} (${user.role})`);
      });
    }

  } catch (error) {
    console.error('Error creating default admin:', error);
    throw error;
  }
}

async function testConnections() {
  try {
    // Test frontend database connection
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Frontend database connection successful');

    // Test live-api connection (if configured)
    const liveApiUrl = process.env.LIVE_API_BASE_URL;
    const adminApiKey = process.env.LIVE_API_ADMIN_KEY;

    if (liveApiUrl && adminApiKey) {
      try {
        const response = await fetch(`${liveApiUrl}/admin/me`, {
          headers: {
            'X-Admin-API-Key': adminApiKey
          }
        });

        if (response.ok) {
          console.log('✅ Live-API connection successful');
        } else {
          console.log('⚠️  Live-API connection failed - check your admin API key');
        }
      } catch (error) {
        console.log('⚠️  Live-API connection failed - check your configuration');
      }
    } else {
      console.log('ℹ️  Live-API configuration not found - please configure LIVE_API_BASE_URL and LIVE_API_ADMIN_KEY');
    }

  } catch (error) {
    console.error('Database connection test failed:', error);
    throw error;
  }
}

function generateApiKey() {
  const crypto = await import('crypto');
  return `admin_${crypto.randomBytes(32).toString('hex')}`;
}

// Run migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main as runAdminBridgeMigration };
