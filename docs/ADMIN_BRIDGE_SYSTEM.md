# Admin Bridge System Documentation

## Overview

The Admin Bridge System provides a secure and comprehensive solution for managing users across both the NextJS frontend database and the live-api database. This system enables seamless user synchronization, credit management, and administrative operations while maintaining database separation for security.

## Architecture

### Database Structure

#### Frontend Database (`POSTGRE_DB`)
- **Users**: NextAuth.js users with enhanced fields for API integration
- **AuditLog**: Comprehensive logging of all admin and user actions
- **AdminUser**: Dedicated admin users for frontend admin access
- **Accounts/Sessions**: NextAuth.js authentication tables
- **Leads**: Lead management system

#### Live-API Database (`POSTGRE_DB_API`)
- **users**: API users with credit and tier management
- **access_tiers**: Service tier definitions
- **api_keys**: API key management
- **api_usage_logs**: Detailed API usage tracking
- **credit_transactions**: Credit transaction history

### Bridge Components

1. **AdminApiService** (`libs/admin-api.js`)
   - Communicates with live-api admin endpoints
   - Handles user registration, credit management, tier operations
   - Provides secure API key-based authentication

2. **Enhanced ApiDatabaseService** (`libs/api-database.js`)
   - Legacy direct database access (fallback)
   - Enhanced sync capabilities using AdminApiService

3. **Admin API Routes** (`app/api/admin/`)
   - User management endpoints
   - Credit management endpoints
   - Tier management endpoints
   - Analytics endpoints

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Live API Integration
LIVE_API_BASE_URL=http://**************:3001
LIVE_API_ADMIN_KEY=admin_api_key_super_secure_change_in_production
```

### Database Migration

Run the migration script to set up the new tables:

```bash
node scripts/migrate-admin-bridge.js
```

## API Endpoints

### User Management

#### List Users
```http
GET /api/admin/users
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
Query Parameters:
  - page: Page number (default: 1)
  - limit: Results per page (default: 20, max: 100)
  - search: Search by email or name
  - role: Filter by user role
```

#### Create User
```http
POST /api/admin/users
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
Body:
{
  "name": "User Name",
  "email": "<EMAIL>",
  "role": "USER",
  "hasAccess": true,
  "priceId": "price_starter",
  "tier_id": 2
}
```

#### Get User Details
```http
GET /api/admin/users/{userId}
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
```

#### Update User
```http
PUT /api/admin/users/{userId}
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
Body:
{
  "name": "Updated Name",
  "role": "ADMIN",
  "isActive": true,
  "hasAccess": true,
  "resetLoginAttempts": true
}
```

#### Deactivate User
```http
DELETE /api/admin/users/{userId}
Authorization: NextAuth session (SUPER_ADMIN role required)
```

### Credit Management

#### Get User Credits
```http
GET /api/admin/users/{userId}/credits
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
```

#### Add Credits
```http
POST /api/admin/users/{userId}/credits
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
Body:
{
  "credits_to_add": 1000,
  "reason": "Promotional bonus"
}
```

#### Reset Monthly Credits
```http
PUT /api/admin/users/{userId}/credits
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
```

### Tier Management

#### Get All Tiers
```http
GET /api/admin/tiers
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
```

#### Get Tier Statistics
```http
GET /api/admin/tiers/{tierId}
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
```

#### Update Tier
```http
PUT /api/admin/tiers/{tierId}
Authorization: NextAuth session (SUPER_ADMIN role required)
Body:
{
  "name": "Updated Tier Name",
  "max_credits_per_month": 10000,
  "max_requests_per_minute": 100,
  "price_per_month": 29.99
}
```

#### Enable/Disable Tier
```http
POST /api/admin/tiers/{tierId}
Authorization: NextAuth session (SUPER_ADMIN role required)
Body:
{
  "action": "enable" // or "disable"
}
```

### Analytics

#### Get Analytics Data
```http
GET /api/admin/analytics
Authorization: NextAuth session (ADMIN/SUPER_ADMIN role required)
Query Parameters:
  - period: "day", "week", "month", "year" (default: "month")
  - limit: Number of top results (default: 10, max: 50)
```

## User Synchronization

### Automatic Sync

Users are automatically synchronized between databases when:
1. User gains API access (hasAccess = true)
2. User subscription changes
3. Admin creates a user with API access

### Manual Sync

Use the AdminApiService to manually sync users:

```javascript
import AdminApiService from '@/libs/admin-api';

const result = await AdminApiService.syncUser({
  id: frontendUser.id,
  email: frontendUser.email,
  name: frontendUser.name,
  priceId: frontendUser.priceId
});
```

### Tier Mapping

Stripe price IDs are mapped to live-api tier IDs:

```javascript
const tierMapping = {
  'price_starter': 2,    // Basic tier
  'price_pro': 3,        // Premium tier
  'price_enterprise': 4  // Enterprise tier
};
```

## Security Features

### Authentication
- NextAuth.js session-based authentication for admin routes
- Role-based access control (USER, ADMIN, SUPER_ADMIN)
- Admin API key authentication for live-api communication

### Rate Limiting
- Different rate limits for different operations
- Per-user and per-IP rate limiting
- Stricter limits for sensitive operations

### Audit Logging
- All admin actions are logged in the AuditLog table
- Includes IP address, user agent, and action details
- Success/failure tracking

### Permission Levels
- **USER**: Regular user access
- **ADMIN**: Can manage users and view analytics
- **SUPER_ADMIN**: Can manage tiers and system configuration

## Error Handling

### Common Error Responses

```json
{
  "error": "Authentication required",
  "statusCode": 401
}

{
  "error": "Insufficient permissions",
  "statusCode": 403
}

{
  "error": "Rate limit exceeded",
  "statusCode": 429,
  "retryAfter": 60
}
```

### Fallback Mechanisms

1. **API Sync Failure**: Falls back to frontend-only operations
2. **Live-API Unavailable**: Graceful degradation with error messages
3. **Database Connection Issues**: Proper error handling and logging

## Monitoring and Maintenance

### Health Checks

Test the system health:

```javascript
import AdminApiService from '@/libs/admin-api';

const isHealthy = await AdminApiService.testConnection();
```

### Database Maintenance

Regular maintenance tasks:
1. Clean up old audit logs
2. Sync user data between databases
3. Monitor API usage and credits

## Troubleshooting

### Common Issues

1. **Live-API Connection Failed**
   - Check LIVE_API_BASE_URL and LIVE_API_ADMIN_KEY
   - Verify live-api server is running
   - Check network connectivity

2. **User Sync Failed**
   - Check user has valid email
   - Verify live-api admin endpoints are accessible
   - Check tier mapping configuration

3. **Permission Denied**
   - Verify user has correct role (ADMIN/SUPER_ADMIN)
   - Check user is active
   - Verify NextAuth session is valid

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed logging for troubleshooting.

## Future Enhancements

1. **Real-time Sync**: WebSocket-based real-time synchronization
2. **Bulk Operations**: Batch user management operations
3. **Advanced Analytics**: More detailed reporting and insights
4. **API Rate Limiting**: Dynamic rate limiting based on user tiers
5. **Automated Tier Management**: Automatic tier upgrades/downgrades based on usage
