// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRE_DB")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?

  // Security: Enhanced user fields
  role          String    @default("USER") // USER, ADMIN, SUPER_ADMIN
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?



  // Security tracking
  passwordChangedAt DateTime?
  twoFactorEnabled  Boolean   @default(false)
  twoFactorSecret   String?

  // API Integration: Link to API database user
  apiUserId     String?   // User ID in the API database
  apiUserEmail  String?   // Email used in API database (for sync verification)

  // Stripe
  customerId    String?   // Stripe customer ID
  priceId       String?   // Stripe price ID
  hasAccess     Boolean   @default(false)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts  Account[]
  sessions  Session[]
  leads     Lead[]
  auditLogs AuditLog[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Application specific models
model Lead {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  source    String?  // Where the lead came from
  ipAddress String?  // For security tracking
  userAgent String?  // For security tracking
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("leads")
}

// Audit Log for tracking admin and user actions
model AuditLog {
  id        String   @id @default(cuid())
  userId    String?  // User who performed the action
  adminId   String?  // Admin who performed the action (if applicable)
  action    String   // Action performed (e.g., 'API_KEY_GENERATED', 'USER_CREATED')
  details   Json?    // Additional details about the action
  ipAddress String?  // IP address of the user/admin
  userAgent String?  // User agent string
  success   Boolean  @default(true) // Whether the action was successful
  createdAt DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}

// Admin Users for frontend admin access
model AdminUser {
  id          String   @id @default(cuid())
  name        String
  email       String   @unique
  role        String   @default("ADMIN") // ADMIN, SUPER_ADMIN
  permissions Json?    // Array of permissions
  isActive    Boolean  @default(true)
  apiKey      String?  @unique // For API access
  lastLoginAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("admin_users")
}


